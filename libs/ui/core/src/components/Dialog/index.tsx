'use client';

import React from 'react';
import classNames from 'classnames';
import { faClose } from '@fortawesome/pro-regular-svg-icons';
import styled from '@benzinga/themetron';
import { Icon } from '../Icon';
import { Button, type ButtonVariant } from '../Button';
import { useDialogPolyfill } from './useDialogPolyfill';
import Hooks from '@benzinga/hooks';

export interface DialogProps {
  actionButtonVariant?: ButtonVariant;
  border?: 'rounded-none' | 'rounded-sm' | 'rounded' | 'rounded-md';
  children?: React.ReactNode;
  closeButtonVariant?: ButtonVariant;
  closeOnOutsideClick?: boolean;
  closeText?: string;
  customButtons?: React.ReactNode;
  actionText?: string;
  lockScroll?: boolean;
  onAction?: () => void;
  onRequestClose?: () => void;
  open: boolean;
  title?: React.ReactElement | string;
  subtitle?: React.ReactElement | string;
  uppercaseTitle?: boolean;
  width?: number;
}

export const Dialog: React.FC<DialogProps> = ({
  actionButtonVariant,
  actionText,
  border = 'rounded-sm',
  children,
  closeButtonVariant,
  closeOnOutsideClick = false,
  closeText,
  customButtons,
  lockScroll = false,
  onAction,
  onRequestClose,
  open,
  subtitle,
  title,
  uppercaseTitle = true,
  width,
}) => {
  const dialogRef = React.useRef<HTMLDialogElement | null>(null);
  const lastActiveElementRef = React.useRef<HTMLElement | null>(null);
  const firstRender = React.useRef<boolean>(true);

  useDialogPolyfill(dialogRef);

  const [, setLocked] = Hooks.useBodyLock(false);

  React.useEffect(() => {
    // Prevents calling imperative methods on mount since the polyfill will throw an error since we are not using the `open` attribute
    if (firstRender.current) {
      firstRender.current = false;
    } else {
      const dialogNode = dialogRef.current;
      if (!dialogNode) return;
      if (open) {
        lastActiveElementRef.current = document.activeElement as HTMLElement;
        dialogNode.showModal();
        lockScroll && setLocked(true);
      } else {
        dialogNode.close();
        lastActiveElementRef?.current?.focus();
        lockScroll && setLocked(false);
      }
    }
  }, [open, lockScroll, setLocked]);

  React.useEffect(() => {
    const dialogElement = dialogRef.current;
    const handleCancel = (event: Event) => {
      event.preventDefault();
      onRequestClose && onRequestClose();
    };
    dialogElement?.addEventListener('cancel', handleCancel);
    return () => {
      dialogElement?.removeEventListener('cancel', handleCancel);
    };
  }, [onRequestClose]);

  const handleOutsideClick = (event: React.MouseEvent<HTMLDialogElement>) => {
    if (closeOnOutsideClick && event.target === event.currentTarget) {
      onRequestClose && onRequestClose();
    }
  };

  const handleAction = React.useCallback(() => {
    onAction && onAction();
  }, [onAction]);

  return (
    <StyledDialog
      className={classNames('max-w-[600px] w-full p-0 cursor-default', {
        [`${`max-w-[${width}px]`}`]: !!width,
        [`${border}`]: !!border,
      })}
      onClick={handleOutsideClick}
      ref={dialogRef}
    >
      <div className="flex flex-col w-full p-4">
        <header className="w-full">
          <div className="flex justify-between items-center w-full">
            <h4 className={classNames('text-base text-xl font-bold', { uppercase: !!uppercaseTitle })}>{title}</h4>
            <button className="p-1 hover:bg-gray-200 rounded" onClick={onRequestClose}>
              <Icon icon={faClose} />
            </button>
          </div>
          {subtitle && <p className="text-gray-500 text-sm mb-2 whitespace-normal">{subtitle}</p>}
        </header>
        <div>{children}</div>
        <form method="dialog">
          <div className="buttons-container mt-4">
            {customButtons ? (
              customButtons
            ) : (
              <div className="flex gap-2 justify-end w-full">
                <Button onClick={onRequestClose} variant={closeButtonVariant ?? 'danger'}>
                  {closeText ?? 'Close'}
                </Button>
                <Button onClick={handleAction} variant={actionButtonVariant ?? 'primary'}>
                  {actionText ?? 'OK'}
                </Button>
              </div>
            )}
          </div>
        </form>
      </div>
    </StyledDialog>
  );
};

export default Dialog;

const StyledDialog = styled.dialog`
  background-color: ${({ theme }) => theme.colorPalette.white};
  //padding: 0.25rem;
  &::backdrop {
    background-color: #00000050;
    background: linear-gradient(rgba(0, 0, 0, 0.251), rgba(0, 0, 0, 0.4));
    animation: fade-in 0.4s;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;
