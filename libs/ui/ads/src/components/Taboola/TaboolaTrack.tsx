import { useEffectDidMount } from '@benzinga/hooks';
import { startTransition } from 'react';

interface TaboolaTrackProps {
  pageType?: 'article' | 'photo';
  loadNewsfeed?: boolean;
}

export const TaboolaTrack: React.FC<TaboolaTrackProps> = ({ pageType = 'article', loadNewsfeed = true }) => {
  const loadHead = () => {
    window['_taboola'] = window['_taboola'] || [];

    // Only push page type if it's not already set
    if (!window['_taboola_page_type_set']) {
      window['_taboola'].push({
        [pageType]: 'auto',
      });
      window['_taboola_page_type_set'] = true;
    }

    const ID = 'tb_loader_script';

    if (!document.getElementById(ID)) {
      const script = document.createElement('script');
      script.id = 'tb_loader_script';
      script.src = '//cdn.taboola.com/libtrc/benzinga-benzinga1/loader.js';
      script.async = true;

      const firstScript = document.getElementsByTagName('script')[0];
      firstScript.parentNode?.insertBefore(script, firstScript);
    }

    if (window.performance && typeof window.performance.mark == 'function') {
      window.performance.mark('tbl_ic');
    }
  };

  const loadBody = () => {
    // Only load newsfeed if no other Taboola units are loaded and loadNewsfeed is true
    if (loadNewsfeed && !window['_taboola_units_loaded']) {
      window['_taboola'] = window['_taboola'] || [];
      window['_taboola'].push({
        container: 'taboola-newsroom',
        mode: 'rbox-tracking',
        placement: 'Newsroom',
      });
    }
  };

  const loadEndBody = () => {
    window['_taboola'] = window['_taboola'] || [];
    window['_taboola'].push({ flush: true });
  };

  useEffectDidMount(() => {
    startTransition(() => {
      loadHead();
      loadBody();
      loadEndBody();
    });
  });

  return loadNewsfeed && !window['_taboola_units_loaded'] ? <div id="taboola-newsroom" /> : null;
};
