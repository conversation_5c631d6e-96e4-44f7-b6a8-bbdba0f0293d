'use client';
import React, { useEffect, useRef, useCallback } from 'react';
import { runningClientSide } from '@benzinga/utils';
import { WidgetType } from './widget';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useUser } from '@benzinga/user-context';

import styles from './floatingWidget.module.scss';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';

declare global {
  interface Window {
    hj?: () => void;
  }
}

interface FloatingWNSTNWidgetProps {
  widgetType?: WidgetType;
  questions?: string[] | null;
  articleID?: number;
  symbol?: string;
  isCrypto?: boolean;
  assetType?: 'CRYPTO' | 'ETF' | 'FX' | 'INDEX' | 'STOCK';
}

interface ChatParams {
  language: string;
  proxy: string;
  userId: string;
  article_index?: string;
  follow_up_questions?: string;
  assetType?: string;
  assetSymbol?: string;
}

export const FloatingWNSTNWidget: React.FC<FloatingWNSTNWidgetProps> = ({
  articleID,
  assetType,
  questions,
  symbol,
  widgetType = WidgetType.FollowUp,
}) => {
  const impressed = useRef<boolean>(false);
  const floatingWidgetContainerRef = useRef<HTMLDivElement>(null);

  const session = React.useContext(SessionContext);
  const user = useUser();

  const checkForFooterAndAdjustPosition = useCallback(() => {
    const adThriveFooter = document.getElementById('AdThrive_Footer_1_phone');

    if (adThriveFooter && floatingWidgetContainerRef.current) {
      const footerRect = adThriveFooter.getBoundingClientRect();
      const footerHeight = footerRect.height;
      const isFooterVisible = footerRect.top < window.innerHeight && footerRect.bottom > 0;
      const isFooterSticky = adThriveFooter.classList.contains('adthrive-sticky');

      if (isFooterVisible && isFooterSticky) {
        const newOffset = footerHeight + 10;
        floatingWidgetContainerRef.current.style.bottom = `${newOffset}px`;
      } else {
        floatingWidgetContainerRef.current.style.bottom = '0px';
      }
    } else if (floatingWidgetContainerRef.current) {
      floatingWidgetContainerRef.current.style.bottom = '0px';
    }
  }, []);

  const handleImpress = useCallback(() => {
    if (!impressed.current && floatingWidgetContainerRef?.current?.style.display !== 'none') {
      impressed.current = true;
      session.getManager(TrackingManager).trackWnstnEvent('wnstn_floating_widget_view', {
        page_link: window.location.href,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadWidget = useCallback(() => {
    if (!runningClientSide()) return;

    const accessType = user?.accessType === 'subscribed' || user?.accessType === 'trialing' ? 'pro' : 'www';
    const chatParams: ChatParams = {
      language: 'EN',
      proxy: `https://wnstn-api.benzinga.com/${accessType}`,
      userId: String(user?.id || ''),
    };

    if (widgetType === WidgetType.FollowUp && articleID) {
      chatParams.article_index = String(articleID);
      if (questions && questions.length > 0) {
        chatParams.follow_up_questions = questions.join(';');
      }
    }

    if (widgetType === WidgetType.Asset) {
      if (assetType) {
        chatParams.assetType = assetType;
      }
      if (symbol) {
        chatParams.assetSymbol = symbol;
      }
    }

    window['chatInitParams'] = chatParams;

    const widgetContainer = floatingWidgetContainerRef?.current?.querySelector('.my-chat-container-container__widget');
    if (widgetContainer) {
      widgetContainer.innerHTML = '';
      const script = document.createElement('script');
      script.id = 'wnstn-widget-script';
      script.src = 'https://staticfiles.wnstn.ai/jsfiles/createFloatingWidget.js';
      script.defer = true;
      script.onload = () => {
        const wnstnIframe = widgetContainer.querySelector('iframe');
        if (wnstnIframe) {
          handleImpress();
          wnstnIframe.onerror = error => {
            console.error('Floating WNSTN Widget Error:', error);
            wnstnIframe.remove();
            floatingWidgetContainerRef?.current?.remove();
          };
        }
        setTimeout(checkForFooterAndAdjustPosition, 100);
      };

      const existingScript = document.getElementById('wnstn-widget-script');
      if (existingScript) {
        existingScript.remove();
      }

      widgetContainer.appendChild(script);
    }
  }, [
    articleID,
    assetType,
    handleImpress,
    questions,
    symbol,
    user?.accessType,
    user?.id,
    widgetType,
    checkForFooterAndAdjustPosition,
  ]);

  const handleTrackWidgetHover = useCallback(() => {
    if (isGlobalImpressionStored('FLOATING_WNSTN')) return;
    try {
      const trackingManager = session?.getManager(TrackingManager);
      if (trackingManager) {
        storeGlobalImpression('FLOATING_WNSTN');
        trackingManager.trackWnstnEvent('wnstn_floating_widget_hover', {
          node_id: articleID ? String(articleID) : undefined,
          page_link: window.location.href,
          symbol,
        });
      }
    } catch (error) {
      alert('Error tracking floating WNSTN widget hover');
      console.error('Error tracking floating WNSTN widget hover:', error);
    }
  }, [articleID, session, symbol]);

  useEffect(() => {
    const hotjarWidgetMutationObserver = new MutationObserver((_mutations, observer) => {
      const widget = document.querySelector('._hj-widget-container');
      if (widget) {
        observer.disconnect();
      }
      if (widget && floatingWidgetContainerRef.current) {
        floatingWidgetContainerRef.current.style.display = 'none';
      }
    });

    hotjarWidgetMutationObserver.observe(document.body, { childList: true, subtree: true });
  }, []);

  useEffect(() => {
    loadWidget();
  }, [loadWidget]);

  useEffect(() => {
    checkForFooterAndAdjustPosition();

    const footerMutationObserver = new MutationObserver(() => {
      checkForFooterAndAdjustPosition();
    });

    footerMutationObserver.observe(document.body, {
      attributeFilter: ['class', 'style'],
      attributes: true,
      childList: true,
      subtree: true,
    });

    const handleScrollResize = () => {
      checkForFooterAndAdjustPosition();
    };

    window.addEventListener('scroll', handleScrollResize, { passive: true });
    window.addEventListener('resize', handleScrollResize, { passive: true });

    return () => {
      footerMutationObserver.disconnect();
      window.removeEventListener('scroll', handleScrollResize);
      window.removeEventListener('resize', handleScrollResize);
    };
  }, [checkForFooterAndAdjustPosition]);

  return (
    <div className={styles.FloatingWidgetContainer} ref={floatingWidgetContainerRef}>
      <div className="my-chat-container" onClick={handleTrackWidgetHover} onMouseOver={handleTrackWidgetHover}>
        <div className="my-chat-container-container__widget"></div>
      </div>
    </div>
  );
};

export default FloatingWNSTNWidget;
