'use client';
import React, { useState } from 'react';
import styled from '@benzinga/themetron';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons/faChevronDown';
import { faChevronUp } from '@fortawesome/pro-solid-svg-icons/faChevronUp';
import { Icon, SectionTitle } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { Product, ProductDataAttribute } from '@benzinga/content-manager';
import { ProductDetails } from '../Product/ProductDetails';
import { ProductRatingContainer } from '../Product/ProductRatingContainer';
import { BzImage } from '@benzinga/image';
import { checkOverflow, sanitizeHTML } from '@benzinga/frontend-utils';
import { Button } from '@benzinga/core-ui';
import { LayoutStacked } from './LayoutStacked';

export interface CompareProductTableProps {
  sectionHash?: string;
  isOpen?: boolean;
  forceOpen?: boolean;
  products: Product[];
  layout?: string;
  customFields?: {
    value: string;
    label: string;
  }[];
}

const getProductAttribute = (product: Product, index: number, findNext?: boolean): ProductDataAttribute | undefined => {
  if (product.data) {
    const attributes = { ...product.data };

    if (!attributes) return undefined;
    const keys = Object.keys(attributes);

    let nextAttribute: ProductDataAttribute | null = null;
    if (findNext) {
      delete attributes[keys[0]];

      for (const key in attributes) {
        if (attributes[key].value) {
          nextAttribute = attributes[key];
        }
      }
    }

    return nextAttribute ?? attributes[keys[index]] ?? undefined;
  } else {
    const attributes = product.attributes;
    if (!attributes) return undefined;

    let attribute = attributes[index];

    if (findNext) {
      if (!attribute || !attribute.value) {
        const targetAttribute = attributes.slice(index, attributes.length - 1).find(item => !!item.value);

        if (targetAttribute) {
          attribute = targetAttribute;
        }
      }
    }

    let attributeValue = attribute.value;

    if (!attributeValue) {
      return undefined;
    }

    if (Array.isArray(attributeValue)) {
      attributeValue = attributeValue[0];
    }

    return {
      display_value: attributeValue ? String(attributeValue) : '',
      label: attribute.title || '',
      value: attributeValue || '',
    };
  }
};

export const CompareProductTable: React.FC<CompareProductTableProps> = ({
  customFields = [],
  forceOpen,
  isOpen,
  layout = 'table',
  products,
  sectionHash,
}) => {
  const [viewMoreDetails, setViewMoreDetails] = useState<string[] | null>([]);

  React.useEffect(() => {
    if (isOpen) {
      setViewMoreDetails(products.map(product => product.name));
    }
  }, [isOpen, products]);

  const handleViewMoreDetails = (name: string) => {
    const newArray = viewMoreDetails ? viewMoreDetails : [];
    const result = newArray.includes(name) ? newArray.filter(i => i !== name) : [...newArray, name];
    setViewMoreDetails(result);
  };

  const isActive = (name: string) => viewMoreDetails && viewMoreDetails.includes(name);
  const isMissingAllDetails = (details: any, disclosure = '') =>
    !details?.desc && !details?.pros?.length && !details?.cons?.length && !details?.best_for?.length && !disclosure;

  const ToggleViewDetails: React.FC<{ product: Product }> = ({ product }) => {
    return !isMissingAllDetails(product.details, product.disclosure) && !forceOpen ? (
      <div className="toggle-product-block flex items-center" onClick={() => handleViewMoreDetails(product.name)}>
        {isActive(product.name) ? (
          <div className="icon-wrapper">
            <Icon icon={faChevronUp} style={{ color: '#99AECC' }} />
          </div>
        ) : (
          <div className="icon-wrapper">
            <Icon icon={faChevronDown} style={{ color: '#99AECC' }} />
          </div>
        )}
      </div>
    ) : null;
  };

  if (!products?.length)
    return (
      <Container>
        <div className="no-items-found">
          <h3>No Items</h3>
        </div>
      </Container>
    );

  if (layout === 'stacked') {
    return <LayoutStacked customFields={customFields} products={products} />;
  }

  // const getProductAttributeBySlug = (product: Product, slug: string): ProductDataAttribute | undefined => {
  //   const attributes = product.data;
  //   if (!attributes) return undefined;
  //   return attributes[slug] || undefined;
  // };

  return (
    <Container className="compare-products-table-container">
      <ul className="compare-products-list" id={sectionHash}>
        {products.map((product: Product) => {
          const attr1 = getProductAttribute(product, 0) ?? {
            display_value: product.best_for || '',
            label: 'Best For:',
            value: product.best_for || '',
          };
          const attr2 = getProductAttribute(product, 1, !product.rating);
          const ratingAttribute = product.rating;
          const btnLabel =
            product?.button_text?.trim() == '' || product.button_text?.toLowerCase() == 'get started'
              ? `Get Started with ${product.company_name ?? product.name}`
              : product.button_text;
          return (
            <li key={product.id}>
              <div className={`list-items-container ${isActive(product.name) ? 'active' : ''}`}>
                {product?.sponsored && (
                  <div className="sponsored-product text-center">
                    <span className="bz-partner">Sponsored</span>
                  </div>
                )}
                <div className="list-items product-info">
                  <div className="flex items-center flex-row">
                    <div className="image-container mr-4">
                      <SmartLink className="image-anchor-wrapper" href={product.link} label="Product Table Image">
                        <BzImage alt={product.name} height={50} src={product.image} width={100} />
                      </SmartLink>
                    </div>
                    <div className={product.image ? 'has-thumbnail' : 'no-thumbnail'}></div>
                  </div>
                  <div className="flex items-center flex-row">
                    {attr2 && attr2?.label !== 'Rating' ? (
                      <Attribute attribute={attr2} product={product} />
                    ) : (
                      <ProductRatingContainer product={product} rating={Number(ratingAttribute)} />
                    )}
                  </div>

                  <div className="flex items-center flex-row product-cta-wrap">
                    <Attribute attribute={attr1} product={product} />
                  </div>
                  {!isMissingAllDetails(product.details, product.disclosure) && (
                    <div className="flex  items-end flex-col product-cta-wrap">
                      <div className="section-title">
                        <span>VIEW PROS & CONS:</span>
                      </div>
                      <div className="product-toggle-wrap">
                        <ToggleViewDetails product={product} />
                      </div>
                    </div>
                  )}

                  <div className="securely-mobile">
                    {product.trust && (
                      <span className="securely" dangerouslySetInnerHTML={{ __html: sanitizeHTML(product.trust) }} />
                    )}
                  </div>
                </div>
                <div className="action-btn">
                  <SmartLink className="cta-link-button" href={product.link} label={'Product Table CTA'}>
                    <Button variant={'flat-blue'}>
                      <span
                        dangerouslySetInnerHTML={{
                          __html: sanitizeHTML(`${btnLabel}`),
                        }}
                      />
                    </Button>
                  </SmartLink>
                </div>
              </div>
              {!isMissingAllDetails(product.details, product.disclosure) && (isActive(product.name) || forceOpen) && (
                <ProductDetails product={product} />
              )}
            </li>
          );
        })}
      </ul>
    </Container>
  );
};

type AttributeValueElementSize = 'medium' | 'large' | 'small';

const Attribute: React.FC<{ product: Product; attribute?: ProductDataAttribute; rating?: boolean }> = ({
  attribute,
}) => {
  const attributeValueElementRef = React.useRef<HTMLDivElement>(null);
  const textLength = attribute?.display_value?.length;
  const [attributeValueElementSize, setAttributeValueElementSize] = React.useState<AttributeValueElementSize>('medium');

  React.useEffect(() => {
    const generateAttributeValueClassname = (): AttributeValueElementSize => {
      const attributeValueElement = attributeValueElementRef.current;
      const isOverflowing = attributeValueElement && checkOverflow(attributeValueElement, true);
      if (!textLength) return 'medium';
      if (textLength < 10 && !isOverflowing) {
        return 'large';
      } else if (textLength >= 10 && textLength < 60) {
        return 'medium';
      } else if (textLength >= 60) {
        return 'small';
      }
      return 'medium';
    };
    const _attributeValueElementSize = generateAttributeValueClassname();
    setAttributeValueElementSize(_attributeValueElementSize);
  }, [textLength]);

  if (attribute?.label && attribute?.display_value) {
    return (
      <div className="attribute-container flex items-center gap-1 flex-col items-end">
        {attribute.label === 'Instructor Image' ? (
          <BzImage alt={attribute.label} height={50} src={attribute.value as string} width={100} />
        ) : (
          <>
            <SectionTitle level={0}>{attribute.label}</SectionTitle>
            <div
              className={`attribute-value ${attributeValueElementSize}`}
              dangerouslySetInnerHTML={{ __html: `${attribute.display_value}` }}
              ref={attributeValueElementRef}
            />
          </>
        )}
      </div>
    );
  } else {
    return <div />;
  }
};

const Container = styled.div`
  border: none;
  margin-bottom: 1rem;
  overflow: hidden;
  .section-title {
    font-size: 0.8rem;
    color: #333;
    font-weight: bold;
  }
  &.compare-products-table-container {
    width: 100%;
    /*height: 100%;*/
    max-width: 1100px;
    margin: 0 auto;
    /*background-color: ${({ theme }) => theme.colorPalette.white}; */
    background-color: transparent;

    .image-container {
      align-items: center;
      .image-anchor-wrapper {
        color: black;
        font-size: 0.9rem;
        text-align: center;
        font-weight: bold;
        .product-name {
          display: none;
        }
      }
      @media (max-width: 499px) {
        margin: 0;
      }
    }
    .has-thumbnail {
      @media (max-width: 499px) {
        display: none;
      }
    }

    .compare-products-list {
      display: flex;
      flex-direction: column;
      list-style: none;
      margin: 0;
      width: 100%;

      > li {
        border-bottom: 1px solid ${({ theme }) => theme.colors.border};
        border: 1px solid #ceddf2;
        border-radius: 4px;
        margin-bottom: 8px;
        background-color: ${({ theme }) => theme.colorPalette.white};

        .list-items-container {
          display: flex;
          flex-direction: column;
          background-color: ${({ theme }) => theme.colorPalette.white};
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-duration: 0.3s;
          transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform,
            filter, backdrop-filter;

          &:hover,
          &.active {
            background-color: ${({ theme }) => theme.colors.backgroundActive};
          }
          .sponsored-product {
            display: flex;
            .bz-partner {
              background: ${({ theme }) => theme.colorPalette.blue500};
              color: white;
              padding: 0 8px 0 5px;
              margin-top: 8px;
              display: inline-block;
              position: relative;
              &::after {
                content: '';
                position: absolute;
                left: 100%;
                top: 0;
                width: 0;
                height: 0;
                border-top: 12px solid ${({ theme }) => theme.colorPalette.blue500};
                border-right: 10px solid transparent;
                border-bottom: 12px solid ${({ theme }) => theme.colorPalette.blue500};
              }
            }
            color: white;
          }
        }

        &:last-of-type {
          /*border-bottom: none; */
        }

        .desktop {
          display: block;
        }

        .mobile {
          display: none;
        }

        @media (max-width: 499px) {
          .desktop {
            display: none;
          }

          .mobile,
          .securely-mobile {
            display: block;
          }
        }
        .securely {
          @media (max-width: 499px) {
            &.mobile {
              display: block;
              font-style: italic;
              font-size: 0.75rem;
              margin-top: 0.5rem;
              color: #5b7292;
            }
          }
        }

        .list-items {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20px 30px;
          justify-content: space-between;
          align-items: center;

          /* .product-attribute-1, */
          .product-attribute-2 {
            display: none;
            margin: auto 0;
          }

          .securely-mobile {
            display: none;
            @media screen and (max-width: 499px) {
              display: block;
              font-style: italic;
              font-size: 0.75rem;
              margin-top: 0.5rem;
              color: #5b7292;
              text-align: center;
            }
          }

          @media screen and (max-width: 500px) {
            .product-attribute-1 {
              text-align: center;
              margin: 0.5rem 0;
              font-size: 1.2rem;
            }
          }

          @media screen and (min-width: 500px) {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 0 10px;

            .product-attribute-1 {
              display: flex;
              width: 150px;
              margin: auto 0;
            }
          }

          @media (min-width: 740px) {
            .product-attribute-2 {
              display: flex;
            }
          }
          &.product-info {
            padding: 20px;
            @media (max-width: 640px) {
              padding: 10px;
            }
          }
          &.attribute-list {
            border-top: 1px solid #e1ebfa;
            padding: 5px 20px;
            align-items: center;
            .review-container {
              flex-direction: row;
              align-items: center;
              gap: 0.4rem;
              .rate-container {
                align-items: center;
              }
            }
            .section-title {
              width: auto;
              font-size: 14px;
              font-weight: normal;
              text-transform: capitalize;
              min-width: 80px;
            }
            .product-attribute-1 {
              width: auto;
              margin: 0;
              font-size: 14px;
              .attribute-value {
                font-weight: 600;
                p {
                  margin-bottom: 0;
                }
              }
            }
          }
          .product-cta-wrap {
            @media (max-width: 499px) {
              width: 100%;
              margin: 10px 0 0;
              justify-content: center;
              align-items: center;
            }
            .get-started-button-container {
              a {
                float: right;
                width: 160px;
                display: block;
              }
              .securely {
                width: 100%;
                clear: both;
                text-align: right;
                @media (max-width: 499px) {
                  display: none;
                }
              }
              button {
                width: 100%;
                margin-bottom: 4px;
              }
            }
            .toggle-product-block {
              cursor: pointer;
              height: 34px;
              width: 34px;
              text-align: center;
              display: flex;
              justify-content: center;
            }
            .attribute-container {
              @media (max-width: 499px) {
                text-align: center;
              }
            }
          }

          .get-started-button-container .cta-link-button {
            display: contents;
          }

          > div {
            display: flex;
            flex-basis: 33.33%;

            h3 {
              font-weight: 700;
              font-size: 1rem;
              line-height: 1.5rem;
              display: inline-block;
              margin-bottom: 0.5rem;

              + span {
                font-size: 1rem;
              }
            }

            &.attribute-container {
              font-size: 1rem;
              .attribute-value,
              .attribute-value > * {
                &.small {
                  font-size: ${({ theme }) => theme.fontSize.sm};
                  line-height: 1.1rem;
                }
                &.medium {
                  font-size: ${({ theme }) => theme.fontSize.lg};
                }
                &.large {
                  font-size: ${({ theme }) => theme.fontSize['2xl']};
                }
              }
              .product-attribute-1 {
                display: none;
                @media screen and (min-width: 768px) {
                  display: block;
                }
              }
            }

            &.review-container {
              display: none;
              @media screen and (min-width: 1024px) {
                display: block;
              }
            }
            &.get-started-button-container {
              width: 208px;
              @media screen and (min-width: 600px) {
                margin-top: 0.5rem;
              }
              @media screen and (min-width: 460px) {
                width: 150px;
                margin-top: 0;
              }
            }
          }
        }
      }

      .toggle-product-block {
        border: 1px solid #ceddf2;
        border-radius: 4px;
        background: #d1e7ff;
      }

      .view-details {
        display: flex;
        align-items: center;
        line-height: inherit;
        color: #2ca2d1;
        text-decoration: none;
        cursor: pointer;
        font-size: 12px;
        .icon-wrapper {
          background-color: #2ca2d1;
          border-radius: 50%;
          width: 11px;
          height: 11px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 5px;

          span {
            font-size: 7px;
          }
        }
      }

      li .product-more-details {
        border: none;
        .product-details-description,
        .product-features-section {
          display: flex;
          flex-direction: column;
          background-color: ${({ theme }) => theme.colorPalette.white};
        }

        .product-details-description {
          /*padding: 1.25rem;
          border-top-width: 1px;
          border-bottom-width: 1px;
          border-color: ${({ theme }) => theme.colors.border};
          width: 100%;
          margin: 0; */
          margin-bottom: 0;

          h2 {
            font-size: 1.25rem;
            line-height: 1.75rem;
            font-weight: 700;
          }

          p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
            text-rendering: optimizeLegibility;
          }
        }

        .product-features-section {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          width: 100%;

          > div {
            padding: 0.875rem;
            flex-grow: 0;
            flex-shrink: 0;
            flex: 1 1 auto;
            min-width: 0;
            width: 100%;

            h4 {
              display: inline-block;
              font-weight: 700;
              font-size: 1rem;
              line-height: 1.5rem;
              margin-bottom: 0.5rem;
            }

            @media (min-width: 640px) {
              border-right: 1px solid ${({ theme }) => theme.colors.border};
              padding: 1.25rem;
              width: 33.333333%;

              &:last-of-type {
                border-right: none;
              }
            }
          }
        }
      }
    }
  }
  .action-btn {
    button {
      width: 100%;
      border-radius: 0;
    }
  }

  .no-items-found {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 50px 0;
  }
`;
