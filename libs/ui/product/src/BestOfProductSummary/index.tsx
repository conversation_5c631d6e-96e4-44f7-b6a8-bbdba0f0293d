import React, { useEffect } from 'react';
import styled from '@benzinga/themetron';
import { Button, Icon } from '@benzinga/core-ui';
import { BzImage } from '@benzinga/image';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons/faChevronDown';
import { SmartLink } from '@benzinga/analytics';
import { BestofItems } from '@benzinga/content-manager';
import { faChevronsDown, faChevronsUp } from '@fortawesome/pro-regular-svg-icons';

export interface BestOfProductSummaryProps {
  label?: string;
  items: BestofItems[];
}

const GoToProductHashButton: React.FC<{ b_id: string }> = ({ b_id }) => {
  return (
    <div className="go-to-product">
      <a className="icon-wrapper" href={`#${b_id}`}>
        <Icon icon={faChevronDown} style={{ color: '#99AECC' }} />
      </a>
    </div>
  );
};

export const BestOfProductSummary: React.FC<BestOfProductSummaryProps> = ({ items, label }) => {
  const [summaryItems, setSummaryItems] = React.useState<BestofItems[]>(items.slice(0, 6));
  const [showAll, setShowAll] = React.useState<boolean>(false);

  const showAllSummary = () => {
    if (!showAll) {
      setSummaryItems(items);
    } else {
      setSummaryItems(items.slice(0, 6));
    }
    setShowAll(!showAll);
  };

  return (
    <BestOfSummaryWrap>
      {label && <h3>{label}</h3>}
      {summaryItems &&
        summaryItems?.length > 0 &&
        summaryItems.map((item, i) =>
          item ? (
            <div className="best-for-card border mb-2" key={i}>
              <div className="flex flex-col sm:flex-row flex-wrap">
                <div className="flex sm:w-1/6 w-full product-image border-b sm:border-r items-center justify-center flex-col ">
                  {item?.sponsored && (
                    <div className="sm:hidden w-full text-center sponsored-product">
                      <span className="">Sponsored</span>
                    </div>
                  )}
                  {item?.image && (
                    <div className="product-logo p-2 ">
                      <BzImage alt={item?.name} height="65px" src={item?.image} width="140px" />
                    </div>
                  )}
                </div>
                <div className="flex sm:w-5/6 w-full flex-col sm:flex-row">
                  <div className="product-detail flex flex-row sm:flex-col w-full h-full justify-between items-center sm:border-b-0 border-b sm:items-start">
                    <div className="flex flex-col w-full">
                      <div className="product-line">
                        {item?.sponsored ? (
                          <span className="hidden sm:block sponsored-product">Sponsored</span>
                        ) : (
                          <span>Best For {item?.best_for}</span>
                        )}
                      </div>
                      <h4 className="px-2 flex items-center sm:py-0 py-2">{item?.name}</h4>
                    </div>
                    <div className="sm:hidden">
                      <GoToProductHashButton b_id={item.b_id} />
                    </div>
                  </div>
                  <div className="flex product-get-started gap-2 items-center p-0 border-l-0 sm:p-2 sm:border-l">
                    <div className="hidden sm:block">
                      <GoToProductHashButton b_id={item.b_id} />
                    </div>
                    <SmartLink className="cta-link-button" href={item?.link} label={`Best Of Summary ${i + 1} `}>
                      <Button size="md" variant="flat-blue">
                        {item?.btn_label || 'Get Started'}
                      </Button>
                    </SmartLink>
                  </div>
                </div>
              </div>
            </div>
          ) : null,
        )}
      {summaryItems.length > 5}{' '}
      {
        <div className="view-more-wrap text-right mb-2 cursor-pointer" onClick={showAllSummary}>
          <span className="view-more flex items-center justify-center gap-1 text-md underline pt-1 pb-2">
            {showAll ? (
              <>
                View Less
                <Icon icon={faChevronsUp} />
              </>
            ) : (
              <>
                View More
                <Icon icon={faChevronsDown} />
              </>
            )}
          </span>
        </div>
      }
    </BestOfSummaryWrap>
  );
};

export const BestOfSummaryWrap = styled.div`
  display: block;
  .best-for-card {
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06);
    .product-get-started {
      a {
        display: block;
        width: max-content;
        button {
          text-transform: capitalize;
        }
        @media (max-width: 640px) {
          width: 100%;
          button {
            width: 100%;
            border-radius: 0;
            padding: 0.25rem;
          }
        }
      }
    }
    .sponsored-product {
      background: #3f83f8;
      color: white !important;
    }
    .product-detail {
      @media (max-width: 640px) {
        padding: 4px 4px 4px 0;
        h4 {
          padding-top: 0;
        }
      }
      .product-line {
        line-height: 1.8;
        margin-bottom: 6px;
        @media (max-width: 640px) {
          margin-bottom: 0;
        }

        span {
          font-size: 14px;
          padding: 0 0.5rem;
          font-weight: 600;
          color: rgb(91, 114, 146);

          @media (max-width: 640px) {
            display: block;
            line-height: 1.2;
            &.sponsored-product {
              display: none;
              padding-top: 4px;
            }
          }
        }
      }
    }

    .go-to-product {
      cursor: pointer;
      height: 34px;
      width: 34px;
      text-align: center;
      display: flex;
      justify-content: center;
      border: 1px solid #ceddf2;
      border-radius: 4px;
      background: #e1ebfa;
      align-items: center;
      .icon-wrapper {
        color: #283d59;
        width: 11px;
        height: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          font-size: 7px;
        }
      }
    }
  }
  .view-more {
    font-size: 14px;
    line-height: 1;
  }
`;
