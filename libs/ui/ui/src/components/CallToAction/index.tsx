'use client';
import React from 'react';

import { Button, ButtonVariant, Text } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { BzImage } from '@benzinga/image';
import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import DisclosureBox from './DisclosureBox';

export type CallToActionLayout = 'default' | 'vertical' | 'floating' | 'popup';

export interface CallToActionProps {
  button_text: string;
  description?: string;
  disclosure?: string;
  floating?: boolean;
  go_link_dofollow?: boolean;
  id?: number;
  image_url?: string;
  image_width?: number;
  layout?: CallToActionLayout;
  onClick?: () => void;
  title: string;
  url: string;
  utm_action?: string;
  utm_category?: string;
  utm_label?: string;
  descriptionLinesCount?: number;
  show_bz_badge?: boolean;
}

export const CallToAction: React.FC<CallToActionProps> = ({
  button_text,
  description,
  descriptionLinesCount,
  disclosure,
  go_link_dofollow,
  image_url,
  image_width,
  layout,
  onClick,
  show_bz_badge = false,
  title,
  url,
  utm_action,
  utm_category,
  utm_label,
}) => {
  const { width } = Hooks.useWindowSize();

  const variant = width < 800 || layout === 'vertical' || layout === 'floating' ? 'vertical' : 'default';

  return (
    <CampaignCallToActionWrapper className={`call-to-action ${variant} ${layout}`}>
      <div className="call-to-action-container">
        <SmartLink
          action={utm_action}
          category={utm_category}
          dofollow={go_link_dofollow}
          href={url}
          label={utm_label}
          onClick={onClick}
        >
          <CallToActionWrapper className="call-to-action-wrapper">
            <div className="promo-image-wrap">
              {show_bz_badge && <span className="bz-partner">Benzinga Partner</span>}

              {image_url && (
                <CallToActionImageWrapper className="image-wrapper">
                  <div>
                    <BzImage alt={title} height={75} src={image_url} width={image_width ?? 120} />
                    <CallToActionButton text={button_text} />
                  </div>
                </CallToActionImageWrapper>
              )}
            </div>
            <div className="call-to-action-copy">
              <div>
                <CallToActionTitle>{title}</CallToActionTitle>
                {description && (
                  <Text
                    className="call-to-action-description"
                    dangerouslySetInnerHTML={{ __html: description }}
                    lines={descriptionLinesCount ? descriptionLinesCount : 5}
                  />
                )}
                <CallToActionButton text={button_text} variant="flat-blue" />
              </div>
            </div>
          </CallToActionWrapper>
        </SmartLink>
        {(layout === 'floating' || layout === 'popup') && disclosure && <DisclosureBox disclosure={disclosure} />}
      </div>

      {!(layout === 'floating' || layout === 'popup') && disclosure && <DisclosureBox disclosure={disclosure} />}
    </CampaignCallToActionWrapper>
  );
};

export const CallToActionButton: React.FC<{ text?: string; variant?: ButtonVariant }> = ({ text, variant }) => {
  return (
    <CallToActionButtonWrapper className="cta-button">
      <Button className="w-full uppercase" variant={variant ? variant : 'flat-blue'}>
        {text ? text : 'Get Started'}
      </Button>
    </CallToActionButtonWrapper>
  );
};

const CampaignCallToActionWrapper = styled.div`
  margin: 16px 0;
  .call-to-action-container {
    border: solid 1px ${({ theme }) => theme.colors.border};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    box-shadow: ${({ theme }) => theme.shadow.default};
    background-color: white;
    padding: 0.25rem 0;
    &:hover {
      box-shadow: ${({ theme }) => theme.shadow.md};
    }
  }
  &.default {
    .call-to-action-copy {
      .cta-button {
        display: none;
      }
    }
  }
  &.floating {
    margin: 0;
  }
  &.vertical,
  &.floating {
    .call-to-action-container {
      margin: 0;
    }
    .call-to-action-wrapper {
      display: inline-flex;
      flex-direction: column;
    }
    .call-to-action-copy {
      flex-direction: column;
    }
    .image-wrapper {
      border-bottom: solid 1px ${({ theme }) => theme.colors.border};
      border-right: none;
      flex-direction: column;
      max-width: 100%;
      min-width: 100%;
      width: 100%;
      margin-bottom: 0;
      .cta-button {
        display: none;
      }
    }
  }
  &.popup {
    .promo-image-wrap {
      .bz-partner {
        margin-top: -1rem;
        margin-left: -1rem;
      }
    }
  }
`;

const CallToActionWrapper = styled.div`
  display: inline-flex;
  flex-direction: row;
  width: 100%;
  position: relative;
  .promo-image-wrap {
    .bz-partner {
      background: ${({ theme }) => theme.colorPalette.blue500};
      color: white;
      padding: 0 8px 0 5px;
      margin-top: 8px;
      display: inline-block;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        left: 100%;
        top: 0;
        width: 0;
        height: 0;
        border-top: 12px solid ${({ theme }) => theme.colorPalette.blue500};
        border-right: 10px solid transparent;
        border-bottom: 12px solid ${({ theme }) => theme.colorPalette.blue500};
      }
    }
    border-right: solid 1px ${({ theme }) => theme.colors.border};
  }

  .image-wrapper {
    max-width: 280px;
    min-width: 280px;
    margin: 0;
  }
  .call-to-action-copy {
    display: inline-flex;
    align-items: center;
    padding: 1rem;
    text-align: left;
    .call-to-action-description {
      color: ${({ theme }) => theme.colors.foreground};
    }
  }
  .cta-button {
    button {
      font-size: 14px;
      font-weight: 600;
      display: inline-flex;
      text-decoration: none;
      align-items: center;
      justify-content: center;
      line-height: 1rem;
      box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
      border: 1px solid rgb(225, 235, 250);
      border-radius: 4px;
      padding: 0.5rem 0.75rem;
      transition:
        background-color 0.25s ease-in-out 0s,
        color 0.25s ease-in-out 0s,
        border-color 0.25s ease-in-out 0s;
    }
  }
`;

const CallToActionTitle = styled.div`
  color: #2ca2d1;
  font-size: ${({ theme }) => theme.fontSize['2xl']};
  font-weight: ${({ theme }) => theme.fontWeight.bold};
  line-height: 2rem;
  margin-bottom: 0.5rem;
`;

const CallToActionImageWrapper = styled.div`
  padding: 1rem;
  display: inline-flex;
  align-items: center;
  width: 100%;
  flex-direction: row;
  div {
    width: 100%;
    text-align: center;
  }
`;

const CallToActionButtonWrapper = styled.div`
  margin-top: 1rem;
  width: 100%;
`;
