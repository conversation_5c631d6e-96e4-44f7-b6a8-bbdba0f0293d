import React from 'react';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { News } from '@benzinga/basic-news-manager';
import { appEnvironment, appName } from '@benzinga/utils';
import { PostCardLayout, PostCardSize, PostCardProps } from './PostCard';
import type { FetchPriority, PreloadOptions } from '@benzinga/image';
import styled from '@benzinga/themetron';
import { PostElapsed } from '../components/PostCard/PostElapsed';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { authorTypeGenerator, calculateReadTime } from '@benzinga/article-manager';

interface EdgePortfolioNewsCardProps extends Partial<PostCardProps> {
  layout?: PostCardLayout;
  node: StoryObject | News;
  size?: PostCardSize;
  sizes?: string;
  relativePath?: boolean;
  imageWidth?: number;
  preloadOptions?: PreloadOptions;
  fetchPriority?: FetchPriority;
}

export const EdgePortfolioNewsCard = ({ node, relativePath }: EdgePortfolioNewsCardProps) => {
  if (!node) return <div />;

  const nodeUrl = relativePath
    ? node.url?.replace('https://www.benzinga.com/', '/')
    : appEnvironment().isApp(appName.india)
      ? node.url?.replace('https://www.benzinga.com/', `${appEnvironment().config().url}/`)
      : node.url;

  const created = 'createdAt' in node ? node.createdAt.toString() : node.created;
  const description = 'teaserText' in node ? node.teaserText : node.teaser;

  return (
    <Container className="portfolio-news-card border rounded" data-action="Edge Portfolio News Card Click">
      <div className="p-4 pb-0">
        <div className="post-time flex flex-row items-center gap-2">
          {created && <PostElapsed created={created} dateType={'med'} showTime={true} />}{' '}
          <div className="time-to-read rounded">{node?.body && calculateReadTime(node?.body)}</div>
        </div>
        <h3>{node?.title && <a dangerouslySetInnerHTML={{ __html: node.title }} href={nodeUrl} />}</h3>
        <div className="author-details flex flex-row mb-4 ">
          <div className="author-by-line">
            by <span>{node.author}</span>
          </div>
        </div>
        {description?.trim() && (
          <div className="border-t-1 py-4">
            <span dangerouslySetInnerHTML={{ __html: sanitizeHTML(description) }} />
          </div>
        )}
      </div>
      <div className="read-full story center border-t-1">
        <a href={nodeUrl}>Read More</a>
      </div>
    </Container>
  );
};

const Container = styled.div`
  &.portfolio-news-card {
    color: #5b7292;
    .border-t-1 {
      border-top: 1px solid #e1ebfa;
    }
    .author-details {
      span {
        color: #192940;
        font-weight: 600;
      }
    }
    .post-time {
      span {
        font-weight: 500;
        font-size: 14px;
        color: #5b7292;
      }
      .time-to-read {
        background: #3f83f80d;
        padding: 2px 6px;
        font-size: 12px;
      }
    }
    h3 {
      a {
        color: #192940;
      }
    }
    .read-full {
      background: #3f83f80d;

      a {
        padding: 8px 0;
        font-size: 14px;
        line-height: 1;
        text-transform: uppercase;
        color: #3f83f8;
        display: block;
        width: 100%;
      }
    }
  }
`;
