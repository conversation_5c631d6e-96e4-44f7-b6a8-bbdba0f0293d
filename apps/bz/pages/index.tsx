import React from 'react';

import styled from '@benzinga/themetron';
import { isBrowser } from '@benzinga/device-utils';
import Hooks from '@benzinga/hooks';
import { MetaProps, PageType } from '@benzinga/seo';
import type { FeaturedVideos } from '@benzinga/videos-manager';
import { BlocksDataContext } from '@benzinga/blocks';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';

import { homeData } from './api/home';
import { HomeProps } from '../src/components/Home/interface';
import { HOME_PAGE_TABS } from '../src/components/Home/components/HomeTabs';
import HomePageTemplate from '../src/components/Home/components/HomePageTemplate';
import { getGlobalSession } from './api/session';
import Error from './_error';

const FloatingWNSTNWidget = React.lazy(() =>
  import('@benzinga/ads').then(module => ({ default: module.FloatingWNSTNWidget })),
);

interface IndexProps extends HomeProps {
  featuredVideos?: FeaturedVideos;
  url?: string;
  pageTargeting?: Record<string, string>;
  error?: number;
}

const Index = (props: IndexProps) => {
  Hooks.useEffectDidMount(() => {
    // Hack to prevent jumping if the last scroll position was at LazyLoaded block
    if (window.history) {
      window.history.scrollRestoration = 'manual';
    }
  });

  if (props.error) {
    return (<Error statusCode={props.error ?? 503} />) as React.ReactElement;
  }

  return (
    <BlocksDataContext.Provider
      value={{
        extraStories: props?.extraStories ?? [],
        featuredNews: props?.featuredNews,
        initialBriefs: props?.briefs ?? [],
      }}
    >
      <HomePageWrapper $page={'home'}>
        <HomePageTemplate featuredNews={props?.featuredNews ?? []} {...props} />
      </HomePageWrapper>
      <FloatingWNSTNWidget />
    </BlocksDataContext.Provider>
  );
};

export const HomePageWrapper = styled.div<{ $page?: string }>`
  @media (max-width: 1300px) {
    .layout-container {
      margin-top: 0.25rem;
    }
  }
  .iframe-wrapper {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    width: 100%;
    margin-bottom: 24px;
    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .ad-wrapper {
    overflow: hidden;
    margin: 16px 0;
    iframe {
      margin: auto;
    }
  }
  .carousel-block {
    max-width: 990px;
  }
  ${({ $page }) =>
    !$page &&
    `
      .raptive-ad-placement {
        margin: 1rem 0;
      }
    `}
  .news-list-group-wrapper {
    .load-more-button {
      text-align: left;
      display: inline-block;
    }
  }
`;

export const MobileAdWrapper = styled.div`
  &.mobile-ad-wrapper {
    display: none;
    height: 250px;
    margin: auto;
    margin-top: 12px;
    text-align: center;
    width: 300px;
    @media (max-width: 800px) {
      display: block;
    }
  }
`;

const findMatchingBlockGroup = (blocks, queryKey, queryValues) => {
  let index = 0;
  const block = blocks.find(block => {
    if (block.blockName === 'acf/news-list-group') {
      return block.attrs.data.groups.find(group =>
        group.query_and_options.query[queryKey]?.some(value => {
          if (queryValues.includes(value)) {
            index = block.attrs.data.groups.indexOf(group);
            return true;
          }
          return false;
        }),
      );
    }
    return false;
  });
  return block ? block.attrs.data.groups[index] : null;
};

export const getServerSideProps = async ({ query, req, res }) => {
  const url = !isBrowser() ? `https://${req.headers.host}` : '';

  if (query.p) {
    return {
      redirect: {
        destination: `/money/preview/${query.p}`,
      },
    };
  }

  const homeProps = await homeData();

  if (!homeProps.post) {
    res.statusCode = 503;
    return {
      props: {
        error: 503,
      },
    };
  }

  homeProps.url = url;
  homeProps.activeTab = HOME_PAGE_TABS.TRENDING;

  const session = getGlobalSession();

  if (Array.isArray(homeProps?.post?.blocks)) {
    homeProps.post.blocks = await loadServerSideBlockData(session, homeProps.post.blocks, req.headers, req.cookies);

    // use topStoriesFeed to replace nodes for block
    const topStoriesBlock = homeProps.post.blocks.find(
      block => block?.blockName === 'acf/news-list-group' && block?.attrs?.data.groups[0]?.title === 'Top Stories',
    );
    if (topStoriesBlock) {
      topStoriesBlock.attrs.data.groups[0].topStoriesFeed = homeProps.topStoriesFeed;
    }

    // disperse partner content
    const partnerLength = homeProps?.partnerContentDisperseMap?.length ?? 0;
    if (homeProps.partnerContentDisperseMap && partnerLength > 0) {
      for (const feed of homeProps.partnerContentDisperseMap) {
        const data = findMatchingBlockGroup(homeProps.post.blocks, feed.queryKey, feed.queryValues);
        if (data) {
          data.nodes?.pop();
          data.nodes?.push(feed.posts[0]);
        }
      }
    }
  }
  if (Array.isArray(homeProps?.post?.sidebar?.blocks)) {
    homeProps.post.sidebar.blocks = await loadServerSideBlockData(
      session,
      homeProps.post.sidebar.blocks,
      req.headers,
      req.cookies,
    );
  }

  const metaProps: MetaProps = {
    canonical: 'https://www.benzinga.com/',
    description:
      homeProps.post?.meta?.description ??
      'Stock Market Quotes, Business News, Financial News, Trading Ideas, and Stock Research by Professionals.',
    image: homeProps.post?.meta?.image ?? null,
    pageType: PageType.Front,
    title: homeProps.post?.meta?.title ?? 'Actionable Trading Ideas, Real-Time News, Financial Insight',
  };

  if (homeProps?.featuredNews?.[0]) {
    metaProps.dateCreated = homeProps.featuredNews?.[0]?.created ?? null;
    metaProps.dateUpdated = homeProps.featuredNews?.[0]?.updated ?? null;
  }

  return {
    props: {
      ...homeProps,
      metaProps,
      pageTargeting: { BZ_PTYPE: 'front' },
    },
  };
};

export default Index;
